<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收费设置 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">收费标准设置</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">基本收费标准</h6>
                            </div>
                            <div class="card-body">
                                <form id="feeForm" onsubmit="saveFeeSettings(event)">
                                    <div class="mb-3">
                                        <label for="hourly_rate" class="form-label">每小时费率（元）</label>
                                        <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" value="{{ fee.hourly_rate }}" min="0" step="0.1" required>
                                        <div class="form-text">设置每小时的基本收费标准</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="daily_max" class="form-label">每日最高费用（元）</label>
                                        <input type="number" class="form-control" id="daily_max" name="daily_max" value="{{ fee.daily_max }}" min="0" step="0.1" required>
                                        <div class="form-text">设置每日收费上限，超过此金额不再增加</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="free_minutes" class="form-label">免费时间（分钟）</label>
                                        <input type="number" class="form-control" id="free_minutes" name="free_minutes" value="{{ fee.free_minutes }}" min="0" step="1" required>
                                        <div class="form-text">设置免费停车时间，在此时间内不收费</div>
                                    </div>
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary px-5">保存设置</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">会员折扣设置</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>会员类型</th>
                                                <th>折扣率</th>
                                                <th>月费（元）</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>普通会员</td>
                                                <td>90%</td>
                                                <td>50</td>
                                            </tr>
                                            <tr>
                                                <td>VIP会员</td>
                                                <td>80%</td>
                                                <td>100</td>
                                            </tr>
                                            <tr>
                                                <td>企业会员</td>
                                                <td>70%</td>
                                                <td>200</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-secondary px-5" id="editDiscountBtn">编辑会员折扣</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 会员折扣编辑模态框 -->
    <div class="modal fade" id="editDiscountModal" tabindex="-1" aria-labelledby="editDiscountModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editDiscountModalLabel">编辑会员折扣</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="discountForm">
                        <div class="mb-3">
                            <label class="form-label fw-bold">普通会员</label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="normal_discount" class="form-label">折扣率 (%)</label>
                                    <input type="number" class="form-control" id="normal_discount" name="normal_discount" value="90" min="0" max="100" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="normal_fee" class="form-label">月费 (元)</label>
                                    <input type="number" class="form-control" id="normal_fee" name="normal_fee" value="50" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">VIP会员</label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="vip_discount" class="form-label">折扣率 (%)</label>
                                    <input type="number" class="form-control" id="vip_discount" name="vip_discount" value="80" min="0" max="100" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="vip_fee" class="form-label">月费 (元)</label>
                                    <input type="number" class="form-control" id="vip_fee" name="vip_fee" value="100" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">企业会员</label>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="corporate_discount" class="form-label">折扣率 (%)</label>
                                    <input type="number" class="form-control" id="corporate_discount" name="corporate_discount" value="70" min="0" max="100" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="corporate_fee" class="form-label">月费 (元)</label>
                                    <input type="number" class="form-control" id="corporate_fee" name="corporate_fee" value="200" min="0" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDiscountSettings()">保存设置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 保存收费设置
        function saveFeeSettings(event) {
            event.preventDefault();

            const form = document.getElementById('feeForm');
            if (form.checkValidity()) {
                // 提交表单
                form.submit();
            } else {
                form.reportValidity();
            }
        }

        // 编辑会员折扣
        document.getElementById('editDiscountBtn').addEventListener('click', function() {
            // 获取当前折扣设置
            {% if discount_settings %}
            document.getElementById('normal_discount').value = "{{ discount_settings.normal.discount }}";
            document.getElementById('normal_fee').value = "{{ discount_settings.normal.fee }}";
            document.getElementById('vip_discount').value = "{{ discount_settings.vip.discount }}";
            document.getElementById('vip_fee').value = "{{ discount_settings.vip.fee }}";
            document.getElementById('corporate_discount').value = "{{ discount_settings.corporate.discount }}";
            document.getElementById('corporate_fee').value = "{{ discount_settings.corporate.fee }}";
            {% endif %}

            const modal = new bootstrap.Modal(document.getElementById('editDiscountModal'));
            modal.show();
        });

        // 保存折扣设置
        function saveDiscountSettings() {
            const form = document.getElementById('discountForm');
            if (form.checkValidity()) {
                // 获取表单数据
                const normalDiscount = document.getElementById('normal_discount').value;
                const normalFee = document.getElementById('normal_fee').value;
                const vipDiscount = document.getElementById('vip_discount').value;
                const vipFee = document.getElementById('vip_fee').value;
                const corporateDiscount = document.getElementById('corporate_discount').value;
                const corporateFee = document.getElementById('corporate_fee').value;

                // 创建表单数据
                const formData = new FormData();
                formData.append('normal_discount', normalDiscount);
                formData.append('normal_fee', normalFee);
                formData.append('vip_discount', vipDiscount);
                formData.append('vip_fee', vipFee);
                formData.append('corporate_discount', corporateDiscount);
                formData.append('corporate_fee', corporateFee);

                // 发送AJAX请求
                fetch('/api/discount/update', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editDiscountModal'));
                        modal.hide();

                        // 显示成功消息
                        alert('会员折扣设置已更新！');

                        // 更新表格中的数据
                        const tbody = document.querySelector('.table-bordered tbody');
                        const rows = tbody.querySelectorAll('tr');

                        // 更新普通会员
                        rows[0].querySelectorAll('td')[1].textContent = normalDiscount + '%';
                        rows[0].querySelectorAll('td')[2].textContent = normalFee;

                        // 更新VIP会员
                        rows[1].querySelectorAll('td')[1].textContent = vipDiscount + '%';
                        rows[1].querySelectorAll('td')[2].textContent = vipFee;

                        // 更新企业会员
                        rows[2].querySelectorAll('td')[1].textContent = corporateDiscount + '%';
                        rows[2].querySelectorAll('td')[2].textContent = corporateFee;
                    } else {
                        alert(data.message || '更新失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('更新折扣设置时发生错误，请重试');
                });
            } else {
                form.reportValidity();
            }
        }

        // 初始化表格数据
        document.addEventListener('DOMContentLoaded', function() {
            {% if discount_settings %}
            const tbody = document.querySelector('.table-bordered tbody');
            const rows = tbody.querySelectorAll('tr');

            // 更新普通会员
            rows[0].querySelectorAll('td')[1].textContent = "{{ discount_settings.normal.discount }}%";
            rows[0].querySelectorAll('td')[2].textContent = "{{ discount_settings.normal.fee }}";

            // 更新VIP会员
            rows[1].querySelectorAll('td')[1].textContent = "{{ discount_settings.vip.discount }}%";
            rows[1].querySelectorAll('td')[2].textContent = "{{ discount_settings.vip.fee }}";

            // 更新企业会员
            rows[2].querySelectorAll('td')[1].textContent = "{{ discount_settings.corporate.discount }}%";
            rows[2].querySelectorAll('td')[2].textContent = "{{ discount_settings.corporate.fee }}";
            {% endif %}
        });
    </script>
</body>
</html>
