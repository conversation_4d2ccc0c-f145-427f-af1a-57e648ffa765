<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传图片识别 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">上传图片识别</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">上传车牌图片</h6>
                            </div>
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data" class="mb-4">
                                    <div class="mb-4 text-center">
                                        <div class="image-preview mb-3">
                                            <img id="preview" src="{{ url_for('static', filename='img/placeholder.png') }}" alt="预览图" class="img-fluid border rounded" style="max-height: 300px;">
                                        </div>
                                        <div class="mb-3">
                                            <label for="plate_image" class="form-label">选择车牌图片</label>
                                            <input class="form-control" type="file" id="plate_image" name="plate_image" accept="image/*" required onchange="previewImage(this)">
                                            <div class="form-text">支持JPG、JPEG、PNG格式，文件大小不超过5MB</div>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-lg px-5">
                                            <i class="bi bi-search me-2"></i>识别车牌
                                        </button>
                                    </div>
                                </form>

                                <div class="mt-4">
                                    <h5>使用说明：</h5>
                                    <ol>
                                        <li>上传包含车牌的清晰图片</li>
                                        <li>系统将自动识别车牌号码</li>
                                        <li>识别成功后，系统会自动记录车辆进出信息</li>
                                        <li>首次识别为入场记录，再次识别同一车牌为出场记录</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                
                reader.onload = function(e) {
                    document.getElementById('preview').src = e.target.result;
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
