#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车费用计算模块
"""

import math
from database.db_manager import db_session
from database.models import ParkingFee, Member, Vehicle

def get_fee_standard():
    """
    获取当前收费标准
    
    Returns:
        收费标准对象
    """
    fee_standard = ParkingFee.query.first()
    if not fee_standard:
        # 如果没有设置收费标准，使用默认值
        fee_standard = ParkingFee(
            hourly_rate=10.0,
            daily_max=100.0,
            free_minutes=15
        )
        db_session.add(fee_standard)
        db_session.commit()
    
    return fee_standard

def get_member_discount(plate_number):
    """
    获取会员折扣率
    
    Args:
        plate_number: 车牌号
        
    Returns:
        折扣率（0-1之间的浮点数），非会员返回1.0
    """
    # 查找车辆
    vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
    
    # 如果车辆存在且关联了会员
    if vehicle and vehicle.member_id:
        member = Member.query.get(vehicle.member_id)
        
        # 检查会员是否有效
        if member and member.is_active:
            return member.discount_rate
    
    # 非会员或会员无效，返回无折扣
    return 1.0

def calculate_fee(plate_number, duration_hours):
    """
    计算停车费用
    
    Args:
        plate_number: 车牌号
        duration_hours: 停车时长（小时）
        
    Returns:
        停车费用（元）
    """
    # 获取收费标准
    fee_standard = get_fee_standard()
    
    # 获取会员折扣
    discount_rate = get_member_discount(plate_number)
    
    # 计算免费时间（小时）
    free_hours = fee_standard.free_minutes / 60
    
    # 扣除免费时间
    billable_hours = max(0, duration_hours - free_hours)
    
    # 如果停车时间少于免费时间，则不收费
    if billable_hours <= 0:
        return 0.0
    
    # 计算基本费用
    base_fee = billable_hours * fee_standard.hourly_rate
    
    # 应用每日最高限额
    days = math.ceil(billable_hours / 24)
    max_fee = days * fee_standard.daily_max
    
    # 取较小值
    fee = min(base_fee, max_fee)
    
    # 应用会员折扣
    discounted_fee = fee * discount_rate
    
    # 四舍五入到小数点后两位
    return round(discounted_fee, 2)

def calculate_monthly_fee(membership_type):
    """
    计算会员月费
    
    Args:
        membership_type: 会员类型
        
    Returns:
        月费（元）
    """
    from database.models import MembershipType
    
    # 查询会员类型
    member_type = MembershipType.query.filter_by(name=membership_type).first()
    
    if member_type:
        return member_type.monthly_fee
    else:
        # 默认月费
        return 0.0

def estimate_fee(hours):
    """
    估算停车费用（不考虑会员折扣）
    
    Args:
        hours: 停车时长（小时）
        
    Returns:
        估算的停车费用（元）
    """
    # 获取收费标准
    fee_standard = get_fee_standard()
    
    # 计算免费时间（小时）
    free_hours = fee_standard.free_minutes / 60
    
    # 扣除免费时间
    billable_hours = max(0, hours - free_hours)
    
    # 如果停车时间少于免费时间，则不收费
    if billable_hours <= 0:
        return 0.0
    
    # 计算基本费用
    base_fee = billable_hours * fee_standard.hourly_rate
    
    # 应用每日最高限额
    days = math.ceil(billable_hours / 24)
    max_fee = days * fee_standard.daily_max
    
    # 取较小值
    fee = min(base_fee, max_fee)
    
    # 四舍五入到小数点后两位
    return round(fee, 2)
