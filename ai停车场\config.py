#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统配置文件
"""

import os

# 应用配置
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here-change-in-production')
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'

# 数据库配置
SQLALCHEMY_DATABASE_URI = 'sqlite:///parking_system.db'
SQLALCHEMY_TRACK_MODIFICATIONS = False

# 上传文件配置
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 车牌识别配置
RECOGNITION_CONFIDENCE_THRESHOLD = 0.7

# 停车场配置
TOTAL_PARKING_SPACES = 100
