#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager

engine = create_engine('sqlite:///parking_system.db')
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
Base = declarative_base()
Base.query = db_session.query_property()

@contextmanager
def session_scope():
    """提供事务范围的上下文管理器"""
    session = db_session()
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.close()

def init_db():
    import database.models
    Base.metadata.create_all(bind=engine)
