#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager

engine = create_engine('sqlite:///parking_system.db')
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
Base = declarative_base()
Base.query = db_session.query_property()

@contextmanager
def session_scope():
    """提供事务范围的上下文管理器"""
    session = db_session()
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.close()

def init_db():
    # 导入所有模型以确保它们被注册
    from database.models import User, Vehicle, ParkingRecord, Member, ParkingFee, MembershipType, FinancialRecord
    Base.metadata.create_all(bind=engine)

    # 创建默认管理员用户（如果不存在）
    try:
        admin_user = db_session.query(User).filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            admin_user.set_password('admin123')  # 默认密码，生产环境中应该修改
            db_session.add(admin_user)
            db_session.commit()
            print("默认管理员账户已创建: admin/admin123")
    except Exception as e:
        print(f"创建默认管理员账户失败: {e}")
        db_session.rollback()
