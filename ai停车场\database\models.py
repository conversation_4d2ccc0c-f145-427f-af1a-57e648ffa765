#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库模型定义
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

Base = declarative_base()

class User(Base, UserMixin):
    """系统用户模型"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Vehicle(Base):
    """车辆信息模型"""
    __tablename__ = 'vehicles'
    
    id = Column(Integer, primary_key=True)
    plate_number = Column(String(20), unique=True, nullable=False)
    vehicle_type = Column(String(20), default='普通车辆')
    created_at = Column(DateTime, default=datetime.now)
    
    # 关联
    member_id = Column(Integer, ForeignKey('members.id'), nullable=True)
    member = relationship("Member", back_populates="vehicles")
    parking_records = relationship("ParkingRecord", back_populates="vehicle")
    
    def __repr__(self):
        return f'<Vehicle {self.plate_number}>'

class ParkingRecord(Base):
    """停车记录模型"""
    __tablename__ = 'parking_records'
    
    id = Column(Integer, primary_key=True)
    plate_number = Column(String(20), nullable=False)
    entry_time = Column(DateTime, default=datetime.now)
    exit_time = Column(DateTime, nullable=True)
    duration = Column(Float, nullable=True)  # 停车时长（小时）
    fee = Column(Float, nullable=True)  # 停车费用
    paid = Column(Boolean, default=False)  # 是否已支付
    payment_method = Column(String(20), nullable=True)  # 支付方式
    payment_time = Column(DateTime, nullable=True)  # 支付时间
    
    # 关联
    vehicle_id = Column(Integer, ForeignKey('vehicles.id'))
    vehicle = relationship("Vehicle", back_populates="parking_records")
    
    def __repr__(self):
        return f'<ParkingRecord {self.plate_number} {self.entry_time}>'

class Member(Base):
    """会员信息模型"""
    __tablename__ = 'members'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    phone = Column(String(20), nullable=False)
    membership_type = Column(String(20), default='普通会员')  # 会员类型：普通会员、VIP会员、企业会员等
    discount_rate = Column(Float, default=1.0)  # 折扣率
    balance = Column(Float, default=0.0)  # 账户余额
    points = Column(Integer, default=0)  # 积分
    start_date = Column(DateTime, default=datetime.now)  # 会员开始日期
    end_date = Column(DateTime, nullable=True)  # 会员结束日期
    is_active = Column(Boolean, default=True)  # 会员是否激活
    created_at = Column(DateTime, default=datetime.now)
    
    # 关联
    vehicles = relationship("Vehicle", back_populates="member")
    
    def __repr__(self):
        return f'<Member {self.name}>'

class ParkingFee(Base):
    """停车费用标准模型"""
    __tablename__ = 'parking_fees'
    
    id = Column(Integer, primary_key=True)
    hourly_rate = Column(Float, nullable=False, default=10.0)  # 每小时费率
    daily_max = Column(Float, nullable=False, default=100.0)  # 每日最高费用
    free_minutes = Column(Integer, default=15)  # 免费停车时间（分钟）
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __repr__(self):
        return f'<ParkingFee {self.hourly_rate}元/小时>'

class MembershipType(Base):
    """会员类型模型"""
    __tablename__ = 'membership_types'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True)
    discount_rate = Column(Float, nullable=False, default=1.0)
    monthly_fee = Column(Float, nullable=False, default=0.0)
    description = Column(String(200))
    created_at = Column(DateTime, default=datetime.now)
    
    def __repr__(self):
        return f'<MembershipType {self.name}>'

class FinancialRecord(Base):
    """财务记录模型"""
    __tablename__ = 'financial_records'
    
    id = Column(Integer, primary_key=True)
    record_type = Column(String(20), nullable=False)  # 收入、支出
    amount = Column(Float, nullable=False)
    description = Column(String(200))
    record_time = Column(DateTime, default=datetime.now)
    
    # 关联
    parking_record_id = Column(Integer, ForeignKey('parking_records.id'), nullable=True)
    
    def __repr__(self):
        return f'<FinancialRecord {self.record_type} {self.amount}>'
