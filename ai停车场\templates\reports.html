<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">统计报表</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 报表筛选 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">报表筛选</h6>
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="report_type" class="form-label">报表类型</label>
                                <select class="form-select" id="report_type" name="type">
                                    <option value="daily" {% if report_type == 'daily' %}selected{% endif %}>日报表</option>
                                    <option value="weekly" {% if report_type == 'weekly' %}selected{% endif %}>周报表</option>
                                    <option value="monthly" {% if report_type == 'monthly' %}selected{% endif %}>月报表</option>
                                    <option value="yearly" {% if report_type == 'yearly' %}selected{% endif %}>年报表</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ report_data.start_date }}">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ report_data.end_date }}">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">生成报表</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 报表概览 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总收入</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ report_data.total_income }} 元</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-currency-yen fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            车辆数量</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ report_data.total_vehicles }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-car-front-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            平均停车时长</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ report_data.avg_duration }} 小时</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-clock-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            会员数量</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ report_data.member_count }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-badge-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表展示 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">收入趋势</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-area">
                                    <div class="text-center py-5">
                                        <p>收入趋势图表将在这里显示</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">车流量趋势</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-area">
                                    <div class="text-center py-5">
                                        <p>车流量趋势图表将在这里显示</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细数据 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">详细数据</h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-file-excel me-1"></i>导出Excel
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>收入（元）</th>
                                        <th>车辆数量</th>
                                        <th>平均停车时长（小时）</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2023-04-15</td>
                                        <td>120.00</td>
                                        <td>15</td>
                                        <td>2.5</td>
                                    </tr>
                                    <tr>
                                        <td>2023-04-16</td>
                                        <td>150.00</td>
                                        <td>18</td>
                                        <td>2.8</td>
                                    </tr>
                                    <tr>
                                        <td>2023-04-17</td>
                                        <td>180.00</td>
                                        <td>22</td>
                                        <td>2.3</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
