{% extends "base.html" %}

{% block title %}上传支付二维码 - 智能停车场管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h2">上传支付二维码</h1>
    </div>

    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">上传支付二维码</h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" action="{{ url_for('qrcode.upload_qrcode') }}">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        微信支付二维码
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="/static/img/your_wechat_qrcode.jpg" id="wechat-preview" class="img-fluid mb-3" style="max-height: 200px;" alt="微信支付二维码预览">
                                        <div class="mb-3">
                                            <label for="wechat_qrcode" class="form-label">选择微信支付二维码图片</label>
                                            <input class="form-control" type="file" id="wechat_qrcode" name="wechat_qrcode" accept="image/*" onchange="previewImage(this, 'wechat-preview')">
                                        </div>
                                        <div class="form-text">上传您的微信收款二维码图片</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        支付宝支付二维码
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="/static/img/your_alipay_qrcode.jpg" id="alipay-preview" class="img-fluid mb-3" style="max-height: 200px;" alt="支付宝支付二维码预览">
                                        <div class="mb-3">
                                            <label for="alipay_qrcode" class="form-label">选择支付宝支付二维码图片</label>
                                            <input class="form-control" type="file" id="alipay_qrcode" name="alipay_qrcode" accept="image/*" onchange="previewImage(this, 'alipay-preview')">
                                        </div>
                                        <div class="form-text">上传您的支付宝收款二维码图片</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <h5>说明：</h5>
                            <ul>
                                <li>请上传清晰的收款二维码图片</li>
                                <li>支持的图片格式：JPG、JPEG、PNG、GIF</li>
                                <li>上传后的二维码将用于停车费支付</li>
                            </ul>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">保存二维码</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function previewImage(input, previewId) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            
            reader.onload = function(e) {
                document.getElementById(previewId).src = e.target.result;
            }
            
            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
{% endblock %}