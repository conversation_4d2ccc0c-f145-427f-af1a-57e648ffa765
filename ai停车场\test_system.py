#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统测试脚本
用于验证系统各个模块是否正常工作
"""

import os
import sys
import traceback

def test_imports():
    """测试所有必要的模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    print("=" * 50)
    
    try:
        # 测试基础依赖
        import flask
        print("✓ Flask 导入成功")
        
        import sqlalchemy
        print("✓ SQLAlchemy 导入成功")
        
        import cv2
        print("✓ OpenCV 导入成功")
        
        import numpy as np
        print("✓ NumPy 导入成功")
        
        import pytesseract
        print("✓ Pytesseract 导入成功")
        
        # 测试项目模块
        from database.db_manager import init_db, db_session
        print("✓ 数据库管理模块导入成功")
        
        from database.models import User, Vehicle, ParkingRecord, Member, ParkingFee
        print("✓ 数据库模型导入成功")
        
        from management.parking_manager import register_entry, register_exit
        print("✓ 停车管理模块导入成功")
        
        from management.member_manager import get_all_members, add_member
        print("✓ 会员管理模块导入成功")
        
        from recognition.plate_recognizer import recognize_plate_from_image
        print("✓ 车牌识别模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        traceback.print_exc()
        return False

def test_database():
    """测试数据库连接和初始化"""
    print("\n" + "=" * 50)
    print("测试数据库...")
    print("=" * 50)
    
    try:
        from database.db_manager import init_db, db_session
        from database.models import User, ParkingFee
        
        # 初始化数据库
        init_db()
        print("✓ 数据库初始化成功")
        
        # 测试查询
        user_count = db_session.query(User).count()
        print(f"✓ 用户表查询成功，当前用户数: {user_count}")
        
        # 测试创建默认收费标准
        fee = db_session.query(ParkingFee).first()
        if not fee:
            fee = ParkingFee(hourly_rate=10.0, daily_max=100.0)
            db_session.add(fee)
            db_session.commit()
            print("✓ 创建默认收费标准成功")
        else:
            print("✓ 收费标准已存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        traceback.print_exc()
        return False

def test_vehicle_detection():
    """测试车辆检测模块"""
    print("\n" + "=" * 50)
    print("测试车辆检测...")
    print("=" * 50)
    
    try:
        from recognition.vehicle_detector import VehicleDetector
        
        detector = VehicleDetector()
        if detector.model is not None:
            print("✓ YOLOv8 模型加载成功")
        else:
            print("⚠ YOLOv8 模型未加载，但模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 车辆检测测试失败: {e}")
        print("⚠ 这可能是因为缺少 ultralytics 库或网络问题")
        return False

def test_config():
    """测试配置文件"""
    print("\n" + "=" * 50)
    print("测试配置...")
    print("=" * 50)
    
    try:
        import config
        
        print(f"✓ SECRET_KEY: {'已设置' if config.SECRET_KEY else '未设置'}")
        print(f"✓ DEBUG: {config.DEBUG}")
        print(f"✓ 数据库URI: {config.SQLALCHEMY_DATABASE_URI}")
        print(f"✓ 上传目录: {config.UPLOAD_FOLDER}")
        
        # 确保上传目录存在
        os.makedirs(config.UPLOAD_FOLDER, exist_ok=True)
        print("✓ 上传目录创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_templates():
    """测试模板文件"""
    print("\n" + "=" * 50)
    print("测试模板文件...")
    print("=" * 50)
    
    required_templates = [
        'index.html',
        'login.html',
        'dashboard.html',
        'upload_image.html',
        'members.html',
        'add_member.html',
        'edit_member.html',
        'records.html',
        'reports.html'
    ]
    
    missing_templates = []
    
    for template in required_templates:
        template_path = os.path.join('templates', template)
        if os.path.exists(template_path):
            print(f"✓ {template}")
        else:
            print(f"✗ {template} (缺失)")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n⚠ 缺失模板文件: {', '.join(missing_templates)}")
        return False
    else:
        print("\n✓ 所有必要的模板文件都存在")
        return True

def main():
    """主测试函数"""
    print("停车场管理系统 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据库", test_database),
        ("车辆检测", test_vehicle_detection),
        ("配置文件", test_config),
        ("模板文件", test_templates)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("\n启动建议:")
        print("1. 运行 'python run.py' 启动系统")
        print("2. 访问 http://localhost:5000")
        print("3. 使用默认账户登录: admin/admin123")
    else:
        print(f"\n⚠ {total - passed} 项测试失败，请检查相关问题。")
        print("\n常见问题解决方案:")
        print("1. 运行 'pip install -r requirements.txt' 安装依赖")
        print("2. 确保 Tesseract-OCR 已正确安装")
        print("3. 检查网络连接（用于下载 YOLOv8 模型）")

if __name__ == "__main__":
    main()
