# 智能停车场管理系统

基于Flask和OpenCV的智能停车场管理系统，支持车牌识别、会员管理、收费计算等功能。

## 系统特性

- 🚗 **智能车牌识别**: 基于OpenCV和Tesseract的车牌识别系统
- 🤖 **车辆检测**: 使用YOLOv8进行车辆检测，避免人像误识别
- 👥 **会员管理**: 完整的会员信息管理和车辆关联
- 💰 **收费管理**: 灵活的收费标准设置和费用计算
- 📊 **数据统计**: 停车记录统计和财务报表
- 🔐 **用户认证**: 基于Flask-Login的用户登录系统
- 📱 **响应式界面**: 基于Bootstrap的现代化Web界面

## 技术栈

- **后端**：Python + Flask
- **数据库**：SQLite/SQLAlchemy
- **图像处理**：OpenCV + Tesseract OCR
- **前端**：Bootstrap 5 + JavaScript

## 快速开始

### 1. 系统要求

- Python 3.8+
- Tesseract-OCR (用于文字识别)
- 网络连接 (用于下载YOLOv8模型)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 安装Tesseract-OCR

**Windows:**
- 下载并安装 [Tesseract-OCR](https://github.com/UB-Mannheim/tesseract/wiki)
- 默认安装路径: `C:\Program Files\Tesseract-OCR\tesseract.exe`

**Linux:**
```bash
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
```

**macOS:**
```bash
brew install tesseract tesseract-lang
```

### 4. 运行系统测试

```bash
python test_system.py
```

### 5. 启动系统

```bash
python run.py
```

系统将在 http://localhost:5000 启动

### 6. 默认登录信息

- 用户名: `admin`
- 密码: `admin123`

## 系统结构

```
parking-system/
├── app.py                 # 主应用入口
├── config.py              # 配置文件
├── database/              # 数据库相关
│   ├── models.py          # 数据库模型
│   └── db_manager.py      # 数据库管理
├── recognition/           # 车牌识别相关
│   ├── plate_recognizer.py # 车牌识别核心
│   ├── video_processor.py  # 视频流处理
│   └── image_processor.py  # 图片处理
├── management/            # 管理功能
│   ├── parking_manager.py  # 停车管理
│   ├── member_manager.py   # 会员管理
│   ├── fee_calculator.py   # 费用计算
│   └── report_generator.py # 报表生成
├── static/                # 静态资源
├── templates/             # HTML模板
└── uploads/               # 上传文件目录
```

## 使用说明

### 车牌识别

1. **实时视频识别**：
   - 进入"实时识别"页面
   - 系统会自动连接摄像头并开始识别车牌
   - 识别到车牌后会自动记录

2. **图片上传识别**：
   - 进入"上传识别"页面
   - 上传包含车牌的图片
   - 系统会自动识别并记录

### 管理功能

1. **停车记录管理**：
   - 查看所有停车记录
   - 支持按车牌号、日期等筛选
   - 导出记录为Excel

2. **会员管理**：
   - 添加/修改/删除会员
   - 设置会员类型和折扣
   - 会员车辆管理

3. **收费设置**：
   - 设置小时费率
   - 设置每日最高费用
   - 设置免费时间

4. **统计报表**：
   - 收入统计
   - 车流量分析
   - 会员统计

## 许可证

MIT License
