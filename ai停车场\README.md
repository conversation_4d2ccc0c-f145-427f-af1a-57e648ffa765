# 智能停车场管理系统

基于Python的智能停车场管理系统，采用先进的图像识别技术实现车牌自动识别与记录。

## 系统功能

- **车牌识别**：支持实时视频流自动抓拍识别和人工上传图片识别
- **出入管理**：自动记录车辆进出时间，智能判断入场和出场
- **计费管理**：根据预设收费标准自动计算停车费用
- **会员管理**：支持会员特权，提供多种会员类型
- **后台管理**：多级管理权限，完善的管理功能

## 技术栈

- **后端**：Python + Flask
- **数据库**：SQLite/SQLAlchemy
- **图像处理**：OpenCV + Tesseract OCR
- **前端**：Bootstrap 5 + JavaScript

## 安装与使用

### 环境要求

- Python 3.6+
- OpenCV
- Tesseract OCR
- 其他依赖库（见requirements.txt）

### 安装步骤

1. 克隆仓库
```
git clone https://github.com/yourusername/parking-system.git
cd parking-system
```

2. 安装依赖
```
pip install -r requirements.txt
```

3. 安装Tesseract OCR
   - Windows: 下载[安装包](https://github.com/UB-Mannheim/tesseract/wiki)
   - Linux: `sudo apt install tesseract-ocr`
   - macOS: `brew install tesseract`

4. 运行应用
```
python app.py
```

5. 访问系统
   - 打开浏览器访问 `http://localhost:5000`
   - 默认管理员账号: admin
   - 默认密码: admin123

## 系统结构

```
parking-system/
├── app.py                 # 主应用入口
├── config.py              # 配置文件
├── database/              # 数据库相关
│   ├── models.py          # 数据库模型
│   └── db_manager.py      # 数据库管理
├── recognition/           # 车牌识别相关
│   ├── plate_recognizer.py # 车牌识别核心
│   ├── video_processor.py  # 视频流处理
│   └── image_processor.py  # 图片处理
├── management/            # 管理功能
│   ├── parking_manager.py  # 停车管理
│   ├── member_manager.py   # 会员管理
│   ├── fee_calculator.py   # 费用计算
│   └── report_generator.py # 报表生成
├── static/                # 静态资源
├── templates/             # HTML模板
└── uploads/               # 上传文件目录
```

## 使用说明

### 车牌识别

1. **实时视频识别**：
   - 进入"实时识别"页面
   - 系统会自动连接摄像头并开始识别车牌
   - 识别到车牌后会自动记录

2. **图片上传识别**：
   - 进入"上传识别"页面
   - 上传包含车牌的图片
   - 系统会自动识别并记录

### 管理功能

1. **停车记录管理**：
   - 查看所有停车记录
   - 支持按车牌号、日期等筛选
   - 导出记录为Excel

2. **会员管理**：
   - 添加/修改/删除会员
   - 设置会员类型和折扣
   - 会员车辆管理

3. **收费设置**：
   - 设置小时费率
   - 设置每日最高费用
   - 设置免费时间

4. **统计报表**：
   - 收入统计
   - 车流量分析
   - 会员统计

## 许可证

MIT License
