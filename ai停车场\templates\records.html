<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车记录 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">停车记录管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="{{ url_for('export_records_filtered', plate_number=request.args.get('plate_number', '')) }}" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-file-earmark-arrow-down me-1"></i>
                            导出CSV
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmClearRecords()">
                            <i class="bi bi-trash me-1"></i>
                            清空记录
                        </button>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 搜索和筛选 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">搜索和筛选</h6>
                    </div>
                    <div class="card-body">
                        <form id="searchForm" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="plate_number" class="form-label">车牌号</label>
                                <input type="text" class="form-control" id="plate_number" name="plate_number" value="{{ request.args.get('plate_number', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">全部</option>
                                    <option value="in" {% if request.args.get('status') == 'in' %}selected{% endif %}>在场</option>
                                    <option value="out" {% if request.args.get('status') == 'out' %}selected{% endif %}>已出场</option>
                                    <option value="paid" {% if request.args.get('status') == 'paid' %}selected{% endif %}>已支付</option>
                                    <option value="unpaid" {% if request.args.get('status') == 'unpaid' %}selected{% endif %}>未支付</option>
                                </select>
                            </div>
                            <div class="col-12 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                    <i class="bi bi-x-circle me-1"></i>重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 停车记录表格 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">停车记录列表</h6>
                        <span class="badge bg-primary">共 {{ records.total }} 条记录</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>车牌号</th>
                                        <th>入场时间</th>
                                        <th>出场时间</th>
                                        <th>停车时长</th>
                                        <th>费用</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in records.items %}
                                    <tr>
                                        <td>{{ record.id }}</td>
                                        <td>{{ record.plate_number }}</td>
                                        <td>{{ record.entry_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td>
                                            {% if record.exit_time %}
                                                {{ record.exit_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                            {% else %}
                                                <span class="badge bg-primary">在场</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.duration %}
                                                {{ record.duration }} 小时
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.fee %}
                                                {{ record.fee }} 元
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.exit_time %}
                                                {% if record.paid %}
                                                    <span class="badge bg-success">已支付</span>
                                                {% else %}
                                                    <span class="badge bg-warning">未支付</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-info">停车中</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewDetails({{ record.id }})">
                                                <i class="bi bi-eye"></i>
                                            </button>

                                            {% if not record.exit_time %}
                                            <!-- 手动出库按钮 -->
                                            <button type="button" class="btn btn-sm btn-warning" onclick="manualExit({{ record.id }})">
                                                <i class="bi bi-box-arrow-right"></i> 手动出库
                                            </button>
                                            {% endif %}

                                            {% if record.exit_time and not record.paid %}
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-success" onclick="markAsPaid({{ record.id }})">
                                                    <i class="bi bi-check-circle"></i> 标记支付
                                                </button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="showQRCodePayment({{ record.id }}, {{ record.fee }})">
                                                    <i class="bi bi-qr-code"></i> 扫码支付
                                                </button>
                                            </div>
                                            {% endif %}

                                            <!-- 删除按钮 -->
                                            <button type="button" class="btn btn-sm btn-danger mt-1" onclick="confirmDeleteRecord({{ record.id }})">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mt-4">
                                {% if records.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_records', page=records.prev_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in records.iter_pages(left_edge=2, right_edge=2, left_current=2, right_current=2) %}
                                    {% if page_num %}
                                        {% if page_num == records.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('view_records', page=page_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if records.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_records', page=records.next_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailsModalLabel">停车记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailsModalBody">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付模态框 -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">标记为已支付</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <input type="hidden" id="record_id" name="record_id">
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">支付方式</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="">请选择支付方式</option>
                                <option value="现金">现金</option>
                                <option value="微信">微信</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行卡">银行卡</option>
                                <option value="会员账户">会员账户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">支付金额</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01" required>
                                <span class="input-group-text">元</span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitPayment()">确认支付</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 二维码支付模态框 -->
    <div class="modal fade" id="qrcodePaymentModal" tabindex="-1" aria-labelledby="qrcodePaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="qrcodePaymentModalLabel">扫码支付</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="qrcode_record_id">
                    <div class="row">
                        <div class="col-md-6 text-center mb-3">
                            <h4>微信支付</h4>
                            <div class="qrcode-container">
                                <img id="wechatQRCode" src="/static/img/your_wechat_qrcode.jpg" width="200" height="200" class="img-thumbnail" alt="微信支付二维码">
                                <p class="mt-2">请使用微信扫描上方二维码</p>
                            </div>
                        </div>
                        <div class="col-md-6 text-center mb-3">
                            <h4>支付宝支付</h4>
                            <div class="qrcode-container">
                                <img id="alipayQRCode" src="/static/img/your_alipay_qrcode.jpg" width="200" height="200" class="img-thumbnail" alt="支付宝支付二维码">
                                <p class="mt-2">请使用支付宝扫描上方二维码</p>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info text-center">
                        <p>请使用微信或支付宝扫描上方二维码进行支付</p>
                        <p>支付金额: <span id="qrcode_amount" class="fw-bold">¥0.00</span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" onclick="confirmQRCodePayment()">已完成支付</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 手动出库模态框 -->
    <div class="modal fade" id="manualExitModal" tabindex="-1" aria-labelledby="manualExitModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="manualExitModalLabel">手动出库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="manualExitForm">
                        <input type="hidden" id="exit_record_id" name="record_id">
                        <div class="mb-3">
                            <label for="exit_time" class="form-label">出场时间</label>
                            <input type="datetime-local" class="form-control" id="exit_time" name="exit_time" required>
                        </div>
                        <div class="mb-3">
                            <label for="exit_note" class="form-label">备注</label>
                            <textarea class="form-control" id="exit_note" name="exit_note" rows="3" placeholder="请输入手动出库原因..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmManualExit()">确认出库</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 查看详情
        function viewDetails(recordId) {
            // 发送AJAX请求获取详细信息
            const detailsModalBody = document.getElementById('detailsModalBody');
            detailsModalBody.innerHTML = `<p>正在加载记录 #${recordId} 的详细信息...</p>`;

            // 显示模态框
            const detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
            detailsModal.show();

            // 发送AJAX请求
            fetch(`/api/records/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const record = data.record;
                        const member = data.member;
                        const discount = data.discount;

                        // 格式化日期
                        const entryTime = new Date(record.entry_time);
                        const exitTime = record.exit_time ? new Date(record.exit_time) : null;

                        // 构建HTML
                        let html = `
                            <div class="mb-3">
                                <h6>基本信息</h6>
                                <p><strong>记录ID:</strong> ${record.id}</p>
                                <p><strong>车牌号:</strong> ${record.plate_number}</p>
                                <p><strong>入场时间:</strong> ${entryTime.toLocaleString()}</p>
                        `;

                        if (exitTime) {
                            html += `
                                <p><strong>出场时间:</strong> ${exitTime.toLocaleString()}</p>
                                <p><strong>停车时长:</strong> ${record.duration} 小时</p>
                                <p><strong>费用:</strong> ${record.fee} 元</p>
                            `;

                            if (record.paid) {
                                html += `<p><strong>状态:</strong> <span class="badge bg-success">已支付</span></p>`;
                                if (record.payment_method) {
                                    html += `<p><strong>支付方式:</strong> ${record.payment_method}</p>`;
                                }
                                if (record.payment_time) {
                                    const paymentTime = new Date(record.payment_time);
                                    html += `<p><strong>支付时间:</strong> ${paymentTime.toLocaleString()}</p>`;
                                }
                            } else {
                                html += `<p><strong>状态:</strong> <span class="badge bg-warning">未支付</span></p>`;
                            }
                        } else {
                            html += `<p><strong>状态:</strong> <span class="badge bg-info">停车中</span></p>`;
                        }

                        html += `</div>`;

                        // 如果是会员，显示会员信息
                        if (member) {
                            html += `
                                <div>
                                    <h6>会员信息</h6>
                                    <p><strong>会员状态:</strong> <span class="badge bg-success">是</span></p>
                                    <p><strong>会员姓名:</strong> ${member.name}</p>
                                    <p><strong>会员类型:</strong> ${member.membership_type}</p>
                            `;

                            if (discount) {
                                html += `<p><strong>折扣率:</strong> ${discount}%</p>`;
                            }

                            html += `</div>`;
                        } else {
                            html += `
                                <div>
                                    <h6>会员信息</h6>
                                    <p><strong>会员状态:</strong> <span class="badge bg-secondary">否</span></p>
                                </div>
                            `;
                        }

                        detailsModalBody.innerHTML = html;
                    } else {
                        detailsModalBody.innerHTML = `<div class="alert alert-danger">${data.message || '加载记录详情失败'}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    detailsModalBody.innerHTML = `<div class="alert alert-danger">加载记录详情时发生错误</div>`;
                });
        }

        // 标记为已支付
        function markAsPaid(recordId) {
            document.getElementById('record_id').value = recordId;

            // 获取记录详情以填充支付金额
            fetch(`/api/records/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.record && data.record.fee) {
                        document.getElementById('payment_amount').value = data.record.fee;

                        // 如果是会员且有折扣，计算折扣后金额
                        if (data.member && data.discount) {
                            const discountedAmount = (data.record.fee * data.discount / 100).toFixed(2);
                            document.getElementById('payment_amount').value = discountedAmount;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });

            // 显示支付模态框
            const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
            paymentModal.show();
        }

        // 提交支付
        function submitPayment() {
            const form = document.getElementById('paymentForm');
            if (form.checkValidity()) {
                const recordId = document.getElementById('record_id').value;
                const paymentMethod = document.getElementById('payment_method').value;
                const paymentAmount = document.getElementById('payment_amount').value;

                // 创建表单数据
                const formData = new FormData();
                formData.append('record_id', recordId);
                formData.append('payment_method', paymentMethod);
                formData.append('payment_amount', paymentAmount);

                // 发送AJAX请求处理支付
                fetch('/api/payment/mark_paid', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
                        paymentModal.hide();

                        // 显示成功消息
                        alert('支付成功！');

                        // 刷新页面
                        location.reload();
                    } else {
                        alert(data.message || '支付失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('支付处理时发生错误，请重试');
                });
            } else {
                form.reportValidity();
            }
        }

        // 重置表单
        function resetForm() {
            document.getElementById('searchForm').reset();
            // 重置后提交表单，以清除URL中的查询参数
            window.location.href = '{{ url_for("view_records") }}';
        }

        // 显示二维码支付
        function showQRCodePayment(recordId, fee) {
            document.getElementById('qrcode_record_id').value = recordId;

            // 获取记录详情以填充支付金额
            fetch(`/api/records/${recordId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.record && data.record.fee) {
                        let amount = data.record.fee;

                        // 如果是会员且有折扣，计算折扣后金额
                        if (data.member && data.discount) {
                            amount = (data.record.fee * data.discount / 100).toFixed(2);
                        }

                        // 设置金额显示
                        document.getElementById('qrcode_amount').textContent = `¥${amount}`;

                        // 设置二维码URL - 使用您自己的图片
                        document.getElementById('wechatQRCode').src = `/static/img/your_wechat_qrcode.jpg`;
                        document.getElementById('alipayQRCode').src = `/static/img/your_alipay_qrcode.jpg`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });

            // 显示二维码支付模态框
            const qrcodeModal = new bootstrap.Modal(document.getElementById('qrcodePaymentModal'));
            qrcodeModal.show();
        }

        // 确认二维码支付
        function confirmQRCodePayment() {
            const recordId = document.getElementById('qrcode_record_id').value;
            const amount = document.getElementById('qrcode_amount').textContent.replace('¥', '');

            // 创建表单数据
            const formData = new FormData();
            formData.append('record_id', recordId);
            formData.append('payment_method', '扫码支付');
            formData.append('payment_amount', amount);

            // 发送AJAX请求处理支付
            fetch('/api/payment/mark_paid', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 关闭模态框
                    const qrcodeModal = bootstrap.Modal.getInstance(document.getElementById('qrcodePaymentModal'));
                    qrcodeModal.hide();

                    // 显示成功消息
                    alert('支付成功！');

                    // 刷新页面
                    location.reload();
                } else {
                    alert(data.message || '支付失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('支付处理时发生错误，请重试');
            });
        }

        // 手动出库
        function manualExit(recordId) {
            document.getElementById('exit_record_id').value = recordId;

            // 设置默认出场时间为当前时间
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            document.getElementById('exit_time').value = `${year}-${month}-${day}T${hours}:${minutes}`;

            // 显示手动出库模态框
            const manualExitModal = new bootstrap.Modal(document.getElementById('manualExitModal'));
            manualExitModal.show();
        }

        // 确认手动出库
        function confirmManualExit() {
            const form = document.getElementById('manualExitForm');
            if (form.checkValidity()) {
                const recordId = document.getElementById('exit_record_id').value;
                const exitTime = document.getElementById('exit_time').value;
                const exitNote = document.getElementById('exit_note').value;

                // 创建表单数据
                const formData = new FormData();
                formData.append('record_id', recordId);
                formData.append('exit_time', exitTime);
                formData.append('exit_note', exitNote);

                // 发送AJAX请求处理手动出库
                fetch('/api/records/manual_exit', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 关闭模态框
                        const manualExitModal = bootstrap.Modal.getInstance(document.getElementById('manualExitModal'));
                        manualExitModal.hide();

                        // 显示成功消息
                        alert('手动出库成功！');

                        // 刷新页面
                        location.reload();
                    } else {
                        alert(data.message || '手动出库失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('手动出库处理时发生错误，请重试');
                });
            } else {
                form.reportValidity();
            }
        }

        // 确认删除单条记录
        function confirmDeleteRecord(recordId) {
            if (confirm('确定要删除ID为 ' + recordId + ' 的停车记录吗？此操作不可恢复！')) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ url_for("delete_parking_record_route", record_id=0) }}'.replace('0', recordId);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 确认清空所有记录
        function confirmClearRecords() {
            if (confirm('确定要导出并清空所有停车记录吗？此操作将先导出所有记录，然后清空页面显示的记录。')) {
                // 先导出记录
                window.location.href = '{{ url_for("export_all_records") }}';

                // 延迟2秒后提示用户记录已导出并提交清空表单
                setTimeout(function() {
                    alert('停车记录已成功导出！请在导出的文件中查看历史记录。');
                    // 提交清空记录的表单
                    document.getElementById('clearRecordsForm').submit();
                }, 2000);
            }
        }
    </script>
    <!-- 隐藏的清空记录表单 -->
    <form id="clearRecordsForm" action="{{ url_for('clear_parking_records_route') }}" method="POST" style="display: none;">
    </form>
</body>
</html>
