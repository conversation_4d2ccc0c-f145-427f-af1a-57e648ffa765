#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统启动脚本
"""

import os
import sys
import webbrowser
from threading import Timer

def open_browser():
    """在默认浏览器中打开应用"""
    webbrowser.open('http://localhost:5000')

if __name__ == "__main__":
    # 确保上传目录存在
    os.makedirs('uploads', exist_ok=True)
    
    # 检查是否已安装依赖
    try:
        import flask
        import sqlalchemy
        import opencv_python
        import pytesseract
    except ImportError:
        print("正在安装依赖库...")
        os.system(f"{sys.executable} -m pip install -r requirements.txt")
    
    # 延迟2秒后自动打开浏览器
    Timer(2, open_browser).start()
    
    # 启动应用
    print("正在启动停车场管理系统...")
    print("请访问 http://localhost:5000")
    
    from app import app
    app.run(debug=True)
