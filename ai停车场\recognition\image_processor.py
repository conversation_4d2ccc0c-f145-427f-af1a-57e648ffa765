#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像处理模块，用于处理上传的图片
"""

import os
import cv2
import numpy as np
from PIL import Image
from recognition.plate_recognizer import recognize_plate_from_image
from recognition.vehicle_detector import has_vehicle

def is_valid_image(file_path):
    """
    检查文件是否为有效图像，并验证图像质量
    
    Args:
        file_path: 文件路径
        
    Returns:
        布尔值，表示文件是否为有效且质量合格的图像
    """
    try:
        # 基本图像验证
        img = Image.open(file_path)
        img.verify()
        
        # 读取图像进行质量检查
        img = cv2.imread(file_path)
        if img is None:
            print(f"无法读取图像: {file_path}")
            return False
            
        # 检查图像尺寸
        height, width = img.shape[:2]
        min_dimension = 300  # 最小尺寸要求
        if width < min_dimension or height < min_dimension:
            print(f"图像尺寸过小: {width}x{height}，最小要求: {min_dimension}x{min_dimension}")
            return False
            
        # 检查图像清晰度
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        min_laplacian = 150  # 提高清晰度要求
        if laplacian_var < min_laplacian:
            print(f"图像不够清晰，清晰度值: {laplacian_var:.2f}，最小要求: {min_laplacian}")
            return False
            
        # 检查图像亮度
        brightness = np.mean(gray)
        min_brightness = 40
        max_brightness = 215
        if brightness < min_brightness:
            print(f"图像过暗，亮度值: {brightness:.2f}，最小要求: {min_brightness}")
            return False
        if brightness > max_brightness:
            print(f"图像过亮，亮度值: {brightness:.2f}，最大要求: {max_brightness}")
            return False
            
        # 检查图像对比度
        contrast = np.std(gray)
        min_contrast = 40
        if contrast < min_contrast:
            print(f"图像对比度过低，对比度值: {contrast:.2f}，最小要求: {min_contrast}")
            return False
            
        return True
    except Exception as e:
        print(f"图像验证出错: {str(e)}")
        return False

def enhance_image(image_path, output_path=None):
    """
    增强图像质量以提高识别率
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径，如果为None则覆盖原图像
        
    Returns:
        增强后的图像路径
    """
    # 如果未指定输出路径，则覆盖原图像
    if output_path is None:
        output_path = image_path
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return image_path
    
    try:
        # 去噪预处理
        denoised = cv2.fastNlMeansDenoisingColored(img, None, 10, 10, 7, 21)
        
        # 光照补偿和色彩增强
        lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        # 增强饱和度
        a = cv2.multiply(a, 1.2)
        b = cv2.multiply(b, 1.2)
        lab = cv2.merge((l, a, b))
        enhanced_color = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        
        # 锐化处理
        kernel_sharpen = np.array([[-1,-1,-1],
                                  [-1, 9,-1],
                                  [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced_color, -1, kernel_sharpen)
        
        # 对比度增强
        alpha = 1.3  # 对比度增强因子
        beta = 0  # 亮度调整
        contrast_enhanced = cv2.convertScaleAbs(sharpened, alpha=alpha, beta=beta)
        
        # 转换为灰度图
        gray = cv2.cvtColor(contrast_enhanced, cv2.COLOR_BGR2GRAY)
        
        # 自适应直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8, 8))
        equalized = clahe.apply(gray)
        
        # 双边滤波，保持边缘的同时去除噪声
        bilateral = cv2.bilateralFilter(equalized, 9, 75, 75)
        
        # Canny边缘检测
        edges = cv2.Canny(bilateral, 100, 200)
        
        # 边缘增强叠加
        result = cv2.addWeighted(bilateral, 0.7, edges, 0.3, 0)
        
        # 保存增强后的图像
        success = cv2.imwrite(output_path, result)
        if not success:
            print(f"保存增强图像失败: {output_path}")
            return image_path
        
        return output_path
        
    except Exception as e:
        print(f"图像增强处理出错: {str(e)}")
        return image_path

def process_uploaded_image(file_path):
    """
    处理上传的图像并识别车牌
    
    Args:
        file_path: 上传的图像文件路径
        
    Returns:
        识别出的车牌号，如果未识别到则返回None
    """
    # 检查文件是否为有效图像
    if not is_valid_image(file_path):
        print(f"无效的图像文件: {file_path}")
        return None
    
    # 检查图像中是否存在车辆
    if not has_vehicle(file_path):
        print(f"未检测到车辆: {file_path}")
        return None
    
    # 增强图像
    enhanced_path = enhance_image(file_path)
    
    # 识别车牌
    plate_number = recognize_plate_from_image(enhanced_path)
    
    return plate_number

def batch_process_images(directory_path):
    """
    批量处理目录中的图像
    
    Args:
        directory_path: 图像目录路径
        
    Returns:
        字典，键为文件名，值为识别出的车牌号
    """
    results = {}
    
    # 遍历目录中的所有文件
    for filename in os.listdir(directory_path):
        # 检查文件是否为图像
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            file_path = os.path.join(directory_path, filename)
            
            # 处理图像并识别车牌
            plate_number = process_uploaded_image(file_path)
            
            # 记录结果
            results[filename] = plate_number
    
    return results

if __name__ == "__main__":
    # 测试代码
    test_image = "test_plate.jpg"  # 替换为实际测试图像路径
    if os.path.exists(test_image):
        plate_number = process_uploaded_image(test_image)
        if plate_number:
            print(f"识别成功，车牌号: {plate_number}")
        else:
            print("未能识别车牌")
    else:
        print(f"测试图像 {test_image} 不存在")
