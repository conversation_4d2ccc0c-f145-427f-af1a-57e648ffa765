#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
上传支付二维码图片处理模块
"""

import os
import shutil
from PIL import Image

def upload_payment_qrcode(file_path, payment_type):
    """
    处理上传的支付二维码图片
    
    参数:
        file_path: 上传的图片临时路径
        payment_type: 支付类型，'wechat'或'alipay'
        
    返回:
        保存后的图片路径
    """
    # 确保目录存在
    os.makedirs('static/img', exist_ok=True)
    
    # 根据支付类型确定目标文件名
    if payment_type == 'wechat':
        target_filename = 'your_wechat_qrcode.jpg'
    elif payment_type == 'alipay':
        target_filename = 'your_alipay_qrcode.jpg'
    else:
        raise ValueError("不支持的支付类型，只支持'wechat'或'alipay'")
    
    target_path = os.path.join('static/img', target_filename)
    
    try:
        # 打开图片验证是否为有效图片
        img = Image.open(file_path)
        
        # 调整图片大小为标准尺寸
        img = img.resize((300, 300))
        
        # 保存处理后的图片
        img.save(target_path)
        print(f"二维码已保存到 {target_path}")
        
        return target_path
    except Exception as e:
        print(f"处理图片时出错: {e}")
        # 如果处理失败，直接复制原文件
        shutil.copy(file_path, target_path)
        return target_path

# 示例使用方法
if __name__ == "__main__":
    # 这里仅作为示例，实际使用时应该从Web应用接收上传的文件
    test_file = "uploads/test_qrcode.jpg"
    if os.path.exists(test_file):
        upload_payment_qrcode(test_file, 'wechat')
        upload_payment_qrcode(test_file, 'alipay')
    else:
        print(f"测试文件 {test_file} 不存在")