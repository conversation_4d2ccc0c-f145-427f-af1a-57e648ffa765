<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑会员 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-camera"></i> 车牌识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-ul"></i> 停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white active" href="{{ url_for('view_members') }}">
                                <i class="bi bi-people"></i> 会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-currency-dollar"></i> 收费管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up"></i> 财务报表
                            </a>
                        </li>
                    </ul>
                    <hr class="text-white">
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-2"></i>
                            <strong>{{ current_user.username }}</strong>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">编辑会员</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="{{ url_for('view_members') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回会员列表
                        </a>
                    </div>
                </div>

                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 编辑会员表单 -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">会员信息</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('edit_member', member_id=member.id) }}">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">会员姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" value="{{ member.name }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="{{ member.phone }}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="plate_number" class="form-label">车牌号 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="plate_number" name="plate_number" 
                                               value="{{ member.plate_number or '' }}" placeholder="例如：京A12345" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="membership_type" class="form-label">会员类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="membership_type" name="membership_type" required>
                                            <option value="">请选择会员类型</option>
                                            <option value="普通会员" {% if member.membership_type == '普通会员' %}selected{% endif %}>普通会员</option>
                                            <option value="VIP会员" {% if member.membership_type == 'VIP会员' %}selected{% endif %}>VIP会员</option>
                                            <option value="企业会员" {% if member.membership_type == '企业会员' %}selected{% endif %}>企业会员</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">开始日期</label>
                                        <input type="text" class="form-control" id="start_date" 
                                               value="{{ member.start_date.strftime('%Y-%m-%d') if member.start_date else '' }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">结束日期</label>
                                        <input type="text" class="form-control" id="end_date" 
                                               value="{{ member.end_date.strftime('%Y-%m-%d') if member.end_date else '永久' }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="balance" class="form-label">账户余额</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="balance" 
                                                   value="{{ '%.2f'|format(member.balance) }}" readonly>
                                            <span class="input-group-text">元</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="points" class="form-label">积分</label>
                                        <input type="text" class="form-control" id="points" 
                                               value="{{ member.points }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="{{ url_for('view_members') }}" class="btn btn-secondary me-md-2">取消</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg"></i> 保存修改
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 车牌号格式验证
        document.getElementById('plate_number').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            e.target.value = value;
            
            // 简单的车牌号格式验证
            const platePattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-Z][A-Z0-9]{5,6}$/;
            if (value && !platePattern.test(value)) {
                e.target.setCustomValidity('请输入正确的车牌号格式');
            } else {
                e.target.setCustomValidity('');
            }
        });

        // 电话号码格式验证
        document.getElementById('phone').addEventListener('input', function(e) {
            const value = e.target.value;
            const phonePattern = /^1[3-9]\d{9}$/;
            if (value && !phonePattern.test(value)) {
                e.target.setCustomValidity('请输入正确的手机号码');
            } else {
                e.target.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
