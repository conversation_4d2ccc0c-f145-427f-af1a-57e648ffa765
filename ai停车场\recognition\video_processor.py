#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频流处理模块，用于实时车牌识别
"""

import cv2
import time
import threading
import os
from datetime import datetime
from recognition.plate_recognizer import recognize_plate_from_image

class VideoProcessor:
    """视频处理器类，用于处理实时视频流"""
    
    def __init__(self, camera_source=0, output_dir='temp_frames', 
                 recognition_interval=2.0, confidence_threshold=0.7):
        """
        初始化视频处理器
        
        Args:
            camera_source: 摄像头源，可以是设备索引或视频文件路径
            output_dir: 临时帧保存目录
            recognition_interval: 识别间隔（秒）
            confidence_threshold: 识别置信度阈值
        """
        self.camera_source = camera_source
        self.output_dir = output_dir
        self.recognition_interval = recognition_interval
        self.confidence_threshold = confidence_threshold
        
        self.cap = None
        self.is_running = False
        self.last_recognition_time = 0
        self.last_cleanup_time = time.time()  # 初始化最后清理时间
        self.recognized_plates = set()  # 已识别的车牌集合，避免短时间内重复识别
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 回调函数
        self.on_plate_recognized = None
    
    def start(self):
        """启动视频处理"""
        if self.is_running:
            return
        
        # 打开摄像头
        self.cap = cv2.VideoCapture(self.camera_source)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开摄像头或视频源: {self.camera_source}")
        
        self.is_running = True
        
        # 在新线程中启动处理
        self.thread = threading.Thread(target=self._process_video)
        self.thread.daemon = True
        self.thread.start()
    
    def stop(self):
        """停止视频处理"""
        self.is_running = False
        if self.thread:
            self.thread.join()
        
        if self.cap:
            self.cap.release()
    
    def set_recognition_callback(self, callback):
        """
        设置车牌识别回调函数
        
        Args:
            callback: 回调函数，接收车牌号作为参数
        """
        self.on_plate_recognized = callback
    
    def _process_video(self):
        """处理视频流"""
        while self.is_running:
            # 读取一帧
            ret, frame = self.cap.read()
            if not ret:
                print("无法读取视频帧")
                break
            
            # 显示实时视频
            cv2.imshow('Parking System - License Plate Recognition', frame)
            
            # 检查是否到达识别间隔
            current_time = time.time()
            if current_time - self.last_recognition_time >= self.recognition_interval:
                # 保存当前帧
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                frame_path = os.path.join(self.output_dir, f"frame_{timestamp}.jpg")
                cv2.imwrite(frame_path, frame)
                
                # 在新线程中进行识别，避免阻塞视频流
                threading.Thread(target=self._recognize_plate, args=(frame_path,)).start()
                
                self.last_recognition_time = current_time
            
            # 按ESC键退出
            if cv2.waitKey(1) == 27:
                break
        
        # 清理资源
        cv2.destroyAllWindows()
    
    def _recognize_plate(self, frame_path):
        """
        识别图像中的车牌
        
        Args:
            frame_path: 图像文件路径
        """
        # 确保使用真实的车牌识别功能，而不是返回固定值
        plate_number = recognize_plate_from_image(frame_path)
        
        # 如果识别成功且不是最近识别过的车牌
        if plate_number and plate_number not in self.recognized_plates:
            print(f"识别到车牌: {plate_number}")
            
            # 添加到已识别集合
            self.recognized_plates.add(plate_number)
            
            # 清理过期的已识别车牌（5分钟后可以重新识别同一车牌）
            current_time = time.time()
            if current_time - self.last_cleanup_time >= 300:  # 5分钟
                self.recognized_plates.clear()
                self.last_cleanup_time = current_time
            
            # 调用回调函数
            if self.on_plate_recognized:
                self.on_plate_recognized(plate_number)
        
        # 删除临时文件
        try:
            os.remove(frame_path)
        except:
            pass

def process_video_stream(camera_source=0, callback=None):
    """
    处理视频流的便捷函数
    
    Args:
        camera_source: 摄像头源
        callback: 识别回调函数
    
    Returns:
        VideoProcessor实例
    """
    processor = VideoProcessor(camera_source=camera_source)
    if callback:
        processor.set_recognition_callback(callback)
    
    processor.start()
    return processor

if __name__ == "__main__":
    # 测试代码
    def test_callback(plate_number):
        print(f"回调函数收到车牌: {plate_number}")
    
    processor = process_video_stream(callback=test_callback)
    
    try:
        # 运行10秒后停止
        time.sleep(10)
    finally:
        processor.stop()
