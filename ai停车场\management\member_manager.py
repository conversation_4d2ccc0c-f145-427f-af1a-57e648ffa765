#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
会员管理模块
"""

from datetime import datetime, timedelta
from database.db_manager import db_session
from database.models import Member, Vehicle, MembershipType

def get_all_members():
    """
    获取所有会员
    
    Returns:
        会员列表
    """
    return Member.query.all()

def get_member_by_id(member_id):
    """
    根据ID获取会员
    
    Args:
        member_id: 会员ID
        
    Returns:
        会员对象，如果不存在则返回None
    """
    return Member.query.get(member_id)

def get_member_by_plate(plate_number):
    """
    根据车牌号获取会员
    
    Args:
        plate_number: 车牌号
        
    Returns:
        会员对象，如果不存在则返回None
    """
    vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
    if vehicle and vehicle.member_id:
        return Member.query.get(vehicle.member_id)
    return None

def add_member(name, plate_number, phone, membership_type, duration_months=12):
    """
    添加新会员
    
    Args:
        name: 会员姓名
        plate_number: 车牌号
        phone: 电话号码
        membership_type: 会员类型
        duration_months: 会员有效期（月）
        
    Returns:
        布尔值，表示操作是否成功
    """
    try:
        # 获取会员类型信息
        member_type = MembershipType.query.filter_by(name=membership_type).first()
        if not member_type:
            print(f"会员类型 {membership_type} 不存在")
            return False
        
        # 计算会员结束日期
        end_date = datetime.now() + timedelta(days=30 * duration_months)
        
        # 创建会员
        member = Member(
            name=name,
            phone=phone,
            membership_type=membership_type,
            discount_rate=member_type.discount_rate,
            start_date=datetime.now(),
            end_date=end_date,
            is_active=True
        )
        db_session.add(member)
        db_session.flush()  # 获取会员ID
        
        # 检查车辆是否存在
        vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
        if not vehicle:
            # 创建新车辆
            vehicle = Vehicle(
                plate_number=plate_number,
                member_id=member.id
            )
            db_session.add(vehicle)
        else:
            # 更新现有车辆的会员关联
            vehicle.member_id = member.id
        
        db_session.commit()
        print(f"会员 {name} 添加成功，会员类型: {membership_type}，车牌号: {plate_number}")
        return True
    
    except Exception as e:
        db_session.rollback()
        print(f"添加会员失败: {str(e)}")
        return False

def update_member(member):
    """
    更新会员信息
    
    Args:
        member: 会员对象
        
    Returns:
        布尔值，表示操作是否成功
    """
    try:
        # 获取会员类型信息
        member_type = MembershipType.query.filter_by(name=member.membership_type).first()
        if member_type:
            member.discount_rate = member_type.discount_rate
        
        db_session.commit()
        print(f"会员 {member.name} 信息更新成功")
        return True
    
    except Exception as e:
        db_session.rollback()
        print(f"更新会员信息失败: {str(e)}")
        return False

def delete_member(member_id):
    """
    删除会员
    
    Args:
        member_id: 会员ID
        
    Returns:
        布尔值，表示操作是否成功
    """
    try:
        member = Member.query.get(member_id)
        if not member:
            print(f"会员ID {member_id} 不存在")
            return False
        
        # 解除车辆关联
        for vehicle in member.vehicles:
            vehicle.member_id = None
        
        # 删除会员
        db_session.delete(member)
        db_session.commit()
        
        print(f"会员 {member.name} 删除成功")
        return True
    
    except Exception as e:
        db_session.rollback()
        print(f"删除会员失败: {str(e)}")
        return False

def renew_membership(member_id, duration_months=12):
    """
    续费会员
    
    Args:
        member_id: 会员ID
        duration_months: 续费时长（月）
        
    Returns:
        布尔值，表示操作是否成功
    """
    try:
        member = Member.query.get(member_id)
        if not member:
            print(f"会员ID {member_id} 不存在")
            return False
        
        # 如果会员已过期，从当前日期开始计算
        if member.end_date and member.end_date < datetime.now():
            member.start_date = datetime.now()
            member.end_date = datetime.now() + timedelta(days=30 * duration_months)
        else:
            # 如果会员未过期，从原结束日期开始续费
            if not member.end_date:
                member.end_date = datetime.now()
            member.end_date = member.end_date + timedelta(days=30 * duration_months)
        
        # 激活会员
        member.is_active = True
        
        db_session.commit()
        print(f"会员 {member.name} 续费成功，新的结束日期: {member.end_date}")
        return True
    
    except Exception as e:
        db_session.rollback()
        print(f"会员续费失败: {str(e)}")
        return False

def check_membership_status():
    """
    检查所有会员状态，将过期会员标记为非活跃
    
    Returns:
        更新的会员数量
    """
    try:
        today = datetime.now().date()
        expired_members = Member.query.filter(
            Member.end_date < today,
            Member.is_active == True
        ).all()
        
        count = 0
        for member in expired_members:
            member.is_active = False
            count += 1
        
        db_session.commit()
        print(f"已将 {count} 个过期会员标记为非活跃")
        return count
    
    except Exception as e:
        db_session.rollback()
        print(f"检查会员状态失败: {str(e)}")
        return 0

def add_vehicle_to_member(member_id, plate_number):
    """
    为会员添加车辆
    
    Args:
        member_id: 会员ID
        plate_number: 车牌号
        
    Returns:
        布尔值，表示操作是否成功
    """
    try:
        member = Member.query.get(member_id)
        if not member:
            print(f"会员ID {member_id} 不存在")
            return False
        
        # 检查车辆是否存在
        vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
        if not vehicle:
            # 创建新车辆
            vehicle = Vehicle(
                plate_number=plate_number,
                member_id=member_id
            )
            db_session.add(vehicle)
        else:
            # 更新现有车辆的会员关联
            vehicle.member_id = member_id
        
        db_session.commit()
        print(f"车辆 {plate_number} 已添加到会员 {member.name}")
        return True
    
    except Exception as e:
        db_session.rollback()
        print(f"为会员添加车辆失败: {str(e)}")
        return False
