#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车辆检测器测试脚本
用于验证车辆检测器的功能和优化效果
"""

import os
from recognition.vehicle_detector import debug_detection

def test_detector():
    print('\n车辆检测器测试\n' + '-'*30)

    # 测试当前图像
    test_image = 'uploads/temp_frame.jpg'
    print(f'\n当前图像: {test_image}')

    # 检查文件是否存在
    if not os.path.exists(test_image):
        print(f"测试图像不存在: {test_image}")
        print("请先上传一张图像进行车牌识别，或者指定其他测试图像路径")
        return

    try:
        result, _ = debug_detection(test_image)
        print(f'\n检测结果: {"有车辆" if result else "无车辆"}')
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return

    # 输出检测参数和优化说明
    print('\n检测逻辑分析:')
    print('1. 置信度阈值: 0.85 (减少误检)')
    print('2. 面积比例范围: 0.05-0.7 (过滤非车辆物体)')
    print('3. 宽高比范围: 0.8-2.0 (符合车辆形状特征)')
    print('4. 人车重叠检测: 重叠度>0.4时判定为误检')
    print('5. 人脸检测: 使用OpenCV级联分类器识别人脸')
    print('\n优化后的检测器能够更准确地区分人像和车辆，避免误识别。')

if __name__ == "__main__":
    test_detector()