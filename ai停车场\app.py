#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统主应用
"""

import os
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import <PERSON><PERSON><PERSON><PERSON>ger, login_user, logout_user, login_required, current_user

from database.db_manager import init_db, db_session
from database.models import User, Vehicle, ParkingRecord, Member, ParkingFee
from recognition.plate_recognizer import recognize_plate_from_image
from recognition.video_processor import process_video_stream
from management.parking_manager import register_entry, register_exit, get_parking_records
from management.member_manager import get_all_members, add_member, update_member, delete_member
from management.fee_calculator import calculate_fee
from management.report_generator import generate_financial_report
# 导入二维码相关模块

# 创建Flask应用
app = Flask(__name__)
app.config.from_pyfile('config.py')

# 初始化数据库
init_db()

# 注册二维码上传路由
from qrcode_routes import qrcode_bp
app.register_blueprint(qrcode_bp, url_prefix='/qrcode')

# 初始化登录管理
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 首页
@app.route('/')
def index():
    return render_template('index.html')

# 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'danger')

    return render_template('login.html')

# 登出
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功登出！', 'success')
    return redirect(url_for('index'))

# 管理员仪表板
@app.route('/dashboard')
@login_required
def dashboard():
    # 获取最近的停车记录
    recent_records = get_parking_records(limit=10)
    # 获取当前停车场状态
    parking_status = {
        'total_spaces': 100,  # 假设总车位数为100
        'occupied_spaces': ParkingRecord.query.filter_by(exit_time=None).count(),
        'available_spaces': 100 - ParkingRecord.query.filter_by(exit_time=None).count()
    }

    # 获取收费标准
    fee = ParkingFee.query.first()
    if not fee:
        fee = ParkingFee(hourly_rate=10.0, daily_max=100.0, free_minutes=15)
        db_session.add(fee)
        db_session.commit()

    # 获取会员数量
    member_count = Member.query.filter_by(is_active=True).count()

    return render_template('dashboard.html',
                         records=recent_records,
                         status=parking_status,
                         now=datetime.now(),
                         fee=fee,
                         member_count=member_count)

# 车牌识别 - 上传图片
@app.route('/recognize/upload', methods=['GET', 'POST'])
@login_required
def recognize_upload():
    if request.method == 'POST':
        if 'plate_image' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        file = request.files['plate_image']
        if file.filename == '':
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        if file:
            # 保存上传的图片
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)

            # 识别车牌
            plate_number = recognize_plate_from_image(filename)

            if plate_number:
                # 记录车辆进出
                record = register_entry(plate_number)
                flash(f'识别成功！车牌号: {plate_number}', 'success')
                return render_template('recognition_result.html', plate_number=plate_number, record=record)
            else:
                flash('无法识别车牌，请重试', 'danger')

    return render_template('upload_image.html')

# 车牌识别 - 实时视频
@app.route('/recognize/video')
@login_required
def recognize_video():
    return render_template('video_recognition.html')

# 车辆记录管理
@app.route('/records')
# 在数据库查询处添加参数化查询
@login_required
def view_records():
    plate_number = request.args.get('plate_number', '')
    # 使用SQLAlchemy的参数化查询
    query = ParkingRecord.query
    if plate_number:
        query = query.filter(ParkingRecord.plate_number.like(f'%{plate_number}%'))
    page = request.args.get('page', 1, type=int)
    per_page = 20
    records = ParkingRecord.query.order_by(ParkingRecord.entry_time.desc()).paginate(page=page, per_page=per_page)
    return render_template('records.html', records=records)

# 删除停车记录
@app.route('/records/delete/<int:record_id>', methods=['POST'])
@login_required
def delete_record(record_id):
    try:
        record = ParkingRecord.query.get_or_404(record_id)
        db_session.delete(record)
        db_session.commit()
        flash('停车记录删除成功！', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'删除停车记录失败: {str(e)}', 'danger')
    return redirect(url_for('view_records'))

# 导出停车记录为CSV
@app.route('/records/export', methods=['GET'])
@login_required
def export_records():
    import csv
    from io import StringIO
    import datetime

    # 获取查询参数
    plate_number = request.args.get('plate_number', '')

    # 构建查询
    query = ParkingRecord.query
    if plate_number:
        query = query.filter(ParkingRecord.plate_number.like(f'%{plate_number}%'))

    # 获取所有记录
    records = query.order_by(ParkingRecord.entry_time.desc()).all()

    # 创建CSV文件
    output = StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow(['ID', '车牌号', '入场时间', '出场时间', '停车时长(小时)', '费用(元)', '支付状态'])

    # 写入数据
    for record in records:
        writer.writerow([
            record.id,
            record.plate_number,
            record.entry_time.strftime('%Y-%m-%d %H:%M:%S') if record.entry_time else '',
            record.exit_time.strftime('%Y-%m-%d %H:%M:%S') if record.exit_time else '',
            record.duration if record.duration else '',
            record.fee if record.fee else '',
            '已支付' if record.paid else '未支付'
        ])

    # 设置响应头
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    response = app.response_class(
        response=output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename=parking_records_{timestamp}.csv'
        }
    )

    return response

# 清空所有停车记录（仅用于导出后清空页面显示）
@app.route('/records/clear', methods=['POST'])
@login_required
def clear_records():
    try:
        # 获取当前在场的车辆（不删除）
        active_records = ParkingRecord.query.filter_by(exit_time=None).all()
        active_ids = [record.id for record in active_records]

        # 删除已经出场的车辆记录
        ParkingRecord.query.filter(ParkingRecord.exit_time != None).delete()
        db_session.commit()

        flash('停车记录已成功清空！当前在场车辆记录已保留。', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'清空停车记录失败: {str(e)}', 'danger')

    return redirect(url_for('view_records'))

# 会员管理
@app.route('/members')
@login_required
def view_members():
    members = get_all_members()
    return render_template('members.html', members=members)

# 添加会员
@app.route('/members/add', methods=['GET', 'POST'])
@login_required
def add_new_member():
    if request.method == 'POST':
        name = request.form['name']
        plate_number = request.form['plate_number']
        phone = request.form['phone']
        membership_type = request.form['membership_type']

        success = add_member(name, plate_number, phone, membership_type)
        if success:
            flash('会员添加成功！', 'success')
            return redirect(url_for('view_members'))
        else:
            flash('添加会员失败，请重试', 'danger')

    return render_template('add_member.html')

# 编辑会员
@app.route('/members/edit/<int:member_id>', methods=['GET', 'POST'])
@login_required
def edit_member(member_id):
    member = Member.query.get_or_404(member_id)

    if request.method == 'POST':
        member.name = request.form['name']
        member.plate_number = request.form['plate_number']
        member.phone = request.form['phone']
        member.membership_type = request.form['membership_type']

        success = update_member(member)
        if success:
            flash('会员信息更新成功！', 'success')
            return redirect(url_for('view_members'))
        else:
            flash('更新会员信息失败，请重试', 'danger')

    return render_template('edit_member.html', member=member)

# 删除会员
@app.route('/members/delete/<int:member_id>', methods=['POST'])
@login_required
def remove_member(member_id):
    success = delete_member(member_id)
    if success:
        flash('会员删除成功！', 'success')
    else:
        flash('删除会员失败，请重试', 'danger')
    return redirect(url_for('view_members'))

# 收费标准设置
@app.route('/fees', methods=['GET', 'POST'])
@login_required
def manage_fees():
    if request.method == 'POST':
        # 更新收费标准
        hourly_rate = float(request.form['hourly_rate'])
        daily_max = float(request.form['daily_max'])

        fee = ParkingFee.query.first()
        if not fee:
            fee = ParkingFee(hourly_rate=hourly_rate, daily_max=daily_max)
            db_session.add(fee)
        else:
            fee.hourly_rate = hourly_rate
            fee.daily_max = daily_max

        db_session.commit()
        flash('收费标准更新成功！', 'success')

    # 获取当前收费标准
    fee = ParkingFee.query.first()
    if not fee:
        fee = ParkingFee(hourly_rate=10.0, daily_max=100.0)  # 默认值
        db_session.add(fee)
        db_session.commit()

    return render_template('manage_fees.html', fee=fee)

# 财务报表
@app.route('/reports')
@login_required
def view_reports():
    report_type = request.args.get('type', 'daily')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    report_data = generate_financial_report(report_type, start_date, end_date)
    return render_template('reports.html', report_data=report_data, report_type=report_type)

# API端点 - 添加会员
@app.route('/api/members/add', methods=['POST'])
@login_required
def api_add_member():
    name = request.form.get('name')
    plate_number = request.form.get('plate_number')
    phone = request.form.get('phone')
    membership_type = request.form.get('membership_type')

    if not all([name, plate_number, phone, membership_type]):
        return jsonify({'success': False, 'message': '所有字段都是必填的'})

    success = add_member(name, plate_number, phone, membership_type)
    if success:
        return jsonify({'success': True, 'message': '会员添加成功'})
    else:
        return jsonify({'success': False, 'message': '添加会员失败，请重试'})

# API端点 - 更新会员
@app.route('/api/members/update', methods=['POST'])
@login_required
def api_update_member():
    member_id = request.form.get('member_id', type=int)
    name = request.form.get('name')
    plate_number = request.form.get('plate_number')
    phone = request.form.get('phone')
    membership_type = request.form.get('membership_type')

    if not all([member_id, name, phone, membership_type]):
        return jsonify({'success': False, 'message': '所有字段都是必填的'})

    try:
        member = Member.query.get_or_404(member_id)
        member.name = name
        member.phone = phone
        member.membership_type = membership_type

        # 更新车牌号
        if plate_number:
            # 先解除原有车辆关联
            for vehicle in member.vehicles:
                vehicle.member_id = None

            # 查找或创建新车辆
            vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
            if not vehicle:
                vehicle = Vehicle(plate_number=plate_number, member_id=member.id)
                db_session.add(vehicle)
            else:
                vehicle.member_id = member.id

        success = update_member(member)
        if success:
            return jsonify({'success': True, 'message': '会员信息更新成功'})
        else:
            return jsonify({'success': False, 'message': '更新会员信息失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

# API端点 - 实时视频识别
@app.route('/api/recognize_video', methods=['POST'])
@login_required
def api_recognize_video():
    if 'video_frame' in request.files:
        file = request.files['video_frame']
        filename = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_frame.jpg')
        file.save(filename)

        plate_number = recognize_plate_from_image(filename)
        if plate_number:
            # 检查是入场还是出场
            record = ParkingRecord.query.filter_by(plate_number=plate_number, exit_time=None).first()
            if record:
                # 已有入场记录，这是出场
                updated_record = register_exit(plate_number)
                return jsonify({
                    'success': True,
                    'plate_number': plate_number,
                    'action': 'exit',
                    'entry_time': updated_record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'exit_time': updated_record.exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'duration': updated_record.duration,
                    'fee': updated_record.fee
                })
            else:
                # 没有入场记录，这是入场
                new_record = register_entry(plate_number)
                return jsonify({
                    'success': True,
                    'plate_number': plate_number,
                    'action': 'entry',
                    'entry_time': new_record.entry_time.strftime('%Y-%m-%d %H:%M:%S')
                })
        else:
            return jsonify({'success': False, 'error': '无法识别车牌'})

    return jsonify({'success': False, 'error': '没有接收到视频帧'})

# 车辆入场处理函数
@app.route('/vehicle_entry', methods=['POST'])
def vehicle_entry():
    # 获取图像并识别车牌
    if 'image' not in request.files:
        return jsonify({
            'success': False,
            'message': '没有上传图像文件'
        }), 400

    image_file = request.files['image']
    if image_file.filename == '':
        return jsonify({
            'success': False,
            'message': '没有选择文件'
        }), 400

    # 保存图像文件
    filename = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_entry.jpg')
    image_file.save(filename)

    # 识别车牌
    plate_number = recognize_plate_from_image(filename)

    # 关键修改：只有在成功识别到车牌号时才录入车辆信息
    if plate_number is not None:
        # 录入车辆信息
        record = register_entry(plate_number)
        if record:
            return jsonify({
                'success': True,
                'plate_number': plate_number,
                'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            return jsonify({
                'success': False,
                'message': '车辆入场登记失败'
            }), 500
    else:
        # 识别失败，返回错误信息
        return jsonify({
            'success': False,
            'message': '无法识别车牌号，请重试或手动输入'
        }), 400

# 清理资源
@app.teardown_appcontext
def shutdown_session(exception=None):
    db_session.remove()

if __name__ == '__main__':
    # 确保上传目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    app.run(debug=True)
