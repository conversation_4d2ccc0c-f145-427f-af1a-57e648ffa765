<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>支付宝支付示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .qr-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .qr-code {
            width: 200px;
            height: 200px;
            background-color: #1677ff;
            margin: 20px auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .qr-code::before {
            content: "支付宝支付示例二维码";
            color: white;
            font-weight: bold;
        }
        h2 {
            color: #1677ff;
            margin-bottom: 10px;
        }
        p {
            color: #333;
            margin-top: 20px;
        }
        .amount {
            font-size: 24px;
            font-weight: bold;
            color: #1677ff;
        }
    </style>
</head>
<body>
    <div class="qr-container">
        <h2>支付宝支付</h2>
        <div class="qr-code"></div>
        <p>请使用支付宝扫描上方二维码进行支付</p>
        <p>支付金额: <span class="amount" id="amount">¥0.00</span></p>
    </div>

    <script>
        // 从URL获取金额参数
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount') || '0.00';
        document.getElementById('amount').textContent = `¥${amount}`;
    </script>
</body>
</html>
